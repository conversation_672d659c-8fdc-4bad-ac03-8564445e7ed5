{"name": "with-nextjs-drizzle-edge", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "fmt": "prettier --write '**/*' --ignore-unknown"}, "dependencies": {"@neondatabase/serverless": "^1.0.0", "autoprefixer": "10.4.16", "drizzle-orm": "0.38.1", "next": "14.2.21", "postcss": "8.4.31", "react": "18.2.0", "react-dom": "18.2.0", "tailwindcss": "3.3.3", "typescript": "5.2.2", "ws": "^8.18.0"}, "devDependencies": {"@types/react": "18.2.25", "@types/react-dom": "18.2.11", "@types/ws": "^8.5.12", "drizzle-kit": "0.30.0", "prettier": "^3.3.3"}}